export interface VideoIdea {
  id: string;
  text: string;
}

export type TitleIdeas = Record<string, string[]>;

export type TitleTone = 'Catchy' | 'Informative' | 'Question-based' | 'SEO-Focused' | 'Humorous' | 'Benefit-Driven' | 'Intriguing' | 'Educational';

export type HookStrategy = 
  | 'General' 
  | 'Problem/Solution' 
  | 'Controversial Statement' 
  | 'Story Teaser' 
  | 'Surprising Fact';

export type TitleLength = 'Short' | 'Standard' | 'Long';

export type VideoStyle = 'General' | 'In-depth Tutorial' | 'Quick Tips' | 'Documentary' | 'Personal Vlog' | 'Product Review' | 'Challenge / Experiment' | 'Let\'s Play / Gameplay';

export type ThumbnailStyle = 'General' | 'High-Energy / Viral' | 'Minimalist & Clean' | 'Aesthetic & Cinematic' | 'Informative / Graphic';

export interface ScriptOutline {
  hook: string;
  intro: string;
  keyPoints: string[];
  callToAction: string;
}

export interface ContentPackage {
  description: string;
  tags: string[];
  tweet: string;
}

export interface ShortFormIdea {
  title: string;
  outline: ScriptOutline;
}