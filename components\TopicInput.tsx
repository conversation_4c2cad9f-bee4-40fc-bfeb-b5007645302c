import React, { useState } from 'react';
import LoadingSpinner from './LoadingSpinner';
import { VideoStyle } from '../types';
import WandIcon from './icons/WandIcon';

interface TopicInputProps {
  onGenerate: (topic: string, style: VideoStyle, isHighCreativity: boolean) => void;
  isLoading: boolean;
  selectedStyle: VideoStyle;
  onStyleChange: (style: VideoStyle) => void;
  isHighCreativity: boolean;
  onHighCreativityChange: (value: boolean) => void;
}

const videoStyles: VideoStyle[] = ['General', 'In-depth Tutorial', 'Quick Tips', 'Documentary', 'Personal Vlog', 'Product Review', 'Challenge / Experiment', 'Let\'s Play / Gameplay'];

const TopicInput: React.FC<TopicInputProps> = ({ onGenerate, isLoading, selectedStyle, onStyleChange, isHighCreativity, onHighCreativityChange }) => {
  const [inputValue, setInputValue] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onGenerate(inputValue, selectedStyle, isHighCreativity);
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-4 bg-zinc-900/50 border border-zinc-800 p-6 rounded-2xl shadow-lg">
      <div>
        <label htmlFor="topic-input" className="text-sm font-medium text-zinc-400 mb-2 block">
            Enter a Topic
        </label>
        <input
          id="topic-input"
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="e.g., 'Retro Gaming' or 'Sustainable Living'"
          className="w-full px-5 py-3 bg-zinc-800 border-2 border-zinc-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-200 placeholder-zinc-500"
          disabled={isLoading}
          aria-label="Video topic"
        />
      </div>

      <div className="border-t border-zinc-800 my-2"></div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:items-end">
        <div>
          <label htmlFor="video-style-selector" className="text-sm font-medium text-zinc-400 mb-2 block">
              Video Style
          </label>
          <div className="relative">
              <select
                  id="video-style-selector"
                  value={selectedStyle}
                  onChange={(e) => onStyleChange(e.target.value as VideoStyle)}
                  disabled={isLoading}
                  className="w-full appearance-none bg-zinc-800 border-2 border-zinc-700 text-zinc-300 text-sm rounded-lg py-3 pl-4 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                  aria-label="Select video style"
              >
                  {videoStyles.map(style => (
                      <option key={style} value={style}>{style}</option>
                  ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-zinc-400">
                  <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
              </div>
          </div>
        </div>
        
        <div className="flex items-center justify-center bg-zinc-800 p-3 rounded-lg border-2 border-zinc-700">
          <label htmlFor="high-creativity-ideas" className="flex items-center gap-3 cursor-pointer">
              <input
                  id="high-creativity-ideas"
                  type="checkbox"
                  checked={isHighCreativity}
                  onChange={(e) => onHighCreativityChange(e.target.checked)}
                  disabled={isLoading}
                  className="h-5 w-5 rounded border-zinc-600 bg-zinc-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-zinc-800"
              />
              <div className="flex flex-col">
                  <span className="font-medium text-zinc-200">High Creativity Mode</span>
                  <span className="text-xs text-zinc-400">Generates more out-of-the-box ideas.</span>
              </div>
          </label>
        </div>
      </div>


      <button
        type="submit"
        disabled={isLoading || !inputValue.trim()}
        className="mt-4 w-full flex justify-center items-center gap-3 px-6 py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-500 disabled:bg-zinc-700 disabled:text-zinc-400 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-[1.02] disabled:scale-100"
      >
        {isLoading ? (
          <>
            <LoadingSpinner />
            Generating...
          </>
        ) : (
          <>
            <WandIcon />
            Generate Ideas
          </>
        )}
      </button>
    </form>
  );
};

export default TopicInput;