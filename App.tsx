import React, { useState, useCallback } from 'react';
import { generateVideoIdeas, generateTitleIdeas, generateVideoHook, generateScriptOutline, generateContentPackage, regenerateVideoIdea, generateThumbnailConcepts, generateShortFormIdeas } from './services/geminiService';
import { VideoIdea, TitleIdeas, TitleTone, TitleLength, VideoStyle, HookStrategy, ScriptOutline, ContentPackage, ThumbnailStyle, ShortFormIdea } from './types';
import TopicInput from './components/TopicInput';
import IdeaList from './components/IdeaList';
import SparklesIcon from './components/icons/SparklesIcon';
import LightbulbIcon from './components/icons/LightbulbIcon';

const App: React.FC = () => {
  const [topic, setTopic] = useState<string>('');
  const [videoIdeas, setVideoIdeas] = useState<VideoIdea[]>([]);
  const [titleIdeas, setTitleIdeas] = useState<TitleIdeas>({});
  const [hooks, setHooks] = useState<Record<string, string>>({});
  const [outlines, setOutlines] = useState<Record<string, ScriptOutline>>({});
  const [contentPackages, setContentPackages] = useState<Record<string, ContentPackage>>({});
  const [thumbnailConcepts, setThumbnailConcepts] = useState<Record<string, string[]>>({});
  const [shortFormIdeas, setShortFormIdeas] = useState<Record<string, ShortFormIdea[]>>({});
  const [isLoadingIdeas, setIsLoadingIdeas] = useState<boolean>(false);
  const [isLoadingTitles, setIsLoadingTitles] = useState<Record<string, boolean>>({});
  const [isLoadingHook, setIsLoadingHook] = useState<Record<string, boolean>>({});
  const [isLoadingOutline, setIsLoadingOutline] = useState<Record<string, boolean>>({});
  const [isLoadingPackage, setIsLoadingPackage] = useState<Record<string, boolean>>({});
  const [isLoadingThumbnails, setIsLoadingThumbnails] = useState<Record<string, boolean>>({});
  const [isLoadingShorts, setIsLoadingShorts] = useState<Record<string, boolean>>({});
  const [isLoadingRegeneration, setIsLoadingRegeneration] = useState<Record<string, boolean>>({});
  const [error, setError] = useState<string | null>(null);
  const [selectedStyle, setSelectedStyle] = useState<VideoStyle>('General');
  const [isHighCreativityIdeas, setIsHighCreativityIdeas] = useState<boolean>(false);

  const handleGenerateIdeas = useCallback(async (newTopic: string, newStyle: VideoStyle, isHighCreativity: boolean) => {
    if (!newTopic.trim()) return;

    setTopic(newTopic);
    setSelectedStyle(newStyle);
    setIsHighCreativityIdeas(isHighCreativity);
    setIsLoadingIdeas(true);
    setError(null);
    setVideoIdeas([]);
    setTitleIdeas({});
    setHooks({});
    setOutlines({});
    setContentPackages({});
    setThumbnailConcepts({});
    setShortFormIdeas({});

    try {
      const ideas = await generateVideoIdeas(newTopic, newStyle, isHighCreativity);
      const ideaObjects: VideoIdea[] = ideas.map((idea, index) => ({ id: `${Date.now()}-${index}`, text: idea }));
      setVideoIdeas(ideaObjects);
    } catch (err) {
      setError('Failed to generate video ideas. Please try again.');
      console.error(err);
    } finally {
      setIsLoadingIdeas(false);
    }
  }, []);

  const handleGenerateTitles = useCallback(async (idea: VideoIdea, tone: TitleTone, length: TitleLength, isHighCreativity: boolean, blacklistWords: string) => {
    setIsLoadingTitles(prev => ({ ...prev, [idea.id]: true }));
    setError(null);

    try {
      const titles = await generateTitleIdeas(idea.text, tone, length, isHighCreativity, blacklistWords, topic);
      setTitleIdeas(prev => ({ ...prev, [idea.id]: titles }));
    } catch (err) {
      setError(`Failed to generate titles for "${idea.text}". Please try again.`);
      console.error(err);
    } finally {
      setIsLoadingTitles(prev => ({ ...prev, [idea.id]: false }));
    }
  }, [topic]);

  const handleGenerateHook = useCallback(async (idea: VideoIdea, strategy: HookStrategy, length: TitleLength, isHighCreativity: boolean) => {
    setIsLoadingHook(prev => ({ ...prev, [idea.id]: true }));
    setError(null);

    try {
      const hook = await generateVideoHook(idea.text, strategy, length, isHighCreativity, topic);
      setHooks(prev => ({ ...prev, [idea.id]: hook }));
    } catch (err) {
      setError(`Failed to generate a hook for "${idea.text}". Please try again.`);
      console.error(err);
    } finally {
      setIsLoadingHook(prev => ({ ...prev, [idea.id]: false }));
    }
  }, [topic]);

  const handleGenerateOutline = useCallback(async (idea: VideoIdea) => {
    setIsLoadingOutline(prev => ({ ...prev, [idea.id]: true }));
    setError(null);

    try {
        const outline = await generateScriptOutline(idea.text, topic);
        setOutlines(prev => ({ ...prev, [idea.id]: outline }));
    } catch (err) {
        setError(`Failed to generate an outline for "${idea.text}". Please try again.`);
        console.error(err);
    } finally {
        setIsLoadingOutline(prev => ({ ...prev, [idea.id]: false }));
    }
  }, [topic]);

  const handleGeneratePackage = useCallback(async (idea: VideoIdea) => {
    setIsLoadingPackage(prev => ({ ...prev, [idea.id]: true }));
    setError(null);

    try {
        const pkg = await generateContentPackage(idea.text, topic);
        setContentPackages(prev => ({ ...prev, [idea.id]: pkg }));
    } catch (err) {
        setError(`Failed to generate a content package for "${idea.text}". Please try again.`);
        console.error(err);
    } finally {
        setIsLoadingPackage(prev => ({ ...prev, [idea.id]: false }));
    }
  }, [topic]);

  const handleGenerateThumbnails = useCallback(async (idea: VideoIdea, style: ThumbnailStyle, keyElements: string) => {
    setIsLoadingThumbnails(prev => ({ ...prev, [idea.id]: true }));
    setError(null);

    try {
        const concepts = await generateThumbnailConcepts(idea.text, topic, style, keyElements);
        setThumbnailConcepts(prev => ({ ...prev, [idea.id]: concepts }));
    } catch (err) {
        setError(`Failed to generate thumbnail concepts for "${idea.text}". Please try again.`);
        console.error(err);
    } finally {
        setIsLoadingThumbnails(prev => ({ ...prev, [idea.id]: false }));
    }
  }, [topic]);
  
  const handleGenerateShorts = useCallback(async (idea: VideoIdea) => {
    setIsLoadingShorts(prev => ({ ...prev, [idea.id]: true }));
    setError(null);

    try {
        const shorts = await generateShortFormIdeas(idea.text, topic);
        setShortFormIdeas(prev => ({ ...prev, [idea.id]: shorts }));
    } catch (err) {
        setError(`Failed to generate short-form ideas for "${idea.text}". Please try again.`);
        console.error(err);
    } finally {
        setIsLoadingShorts(prev => ({ ...prev, [idea.id]: false }));
    }
  }, [topic]);

  const handleRegenerateIdea = useCallback(async (ideaId: string) => {
    setIsLoadingRegeneration(prev => ({ ...prev, [ideaId]: true }));
    setError(null);

    const existingIdeas = videoIdeas.map(i => i.text);

    try {
        const newIdeaText = await regenerateVideoIdea(topic, selectedStyle, isHighCreativityIdeas, existingIdeas);
        
        setVideoIdeas(prevIdeas => prevIdeas.map(idea => 
            idea.id === ideaId ? { ...idea, text: newIdeaText } : idea
        ));

        // Clear associated generated content for the old idea
        const clearContent = (setter: React.Dispatch<React.SetStateAction<Record<string, any>>>) => {
          setter(prev => {
              const newState = { ...prev };
              delete newState[ideaId];
              return newState;
          });
        };

        clearContent(setTitleIdeas);
        clearContent(setHooks);
        clearContent(setOutlines);
        clearContent(setContentPackages);
        clearContent(setThumbnailConcepts);
        clearContent(setShortFormIdeas);

    } catch (err) {
        setError('Failed to regenerate the video idea. Please try again.');
        console.error(err);
    } finally {
        setIsLoadingRegeneration(prev => ({ ...prev, [ideaId]: false }));
    }
  }, [topic, selectedStyle, isHighCreativityIdeas, videoIdeas]);


  return (
    <div className="min-h-screen bg-zinc-950 text-zinc-200 selection:bg-blue-500 selection:text-white">
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80" aria-hidden="true">
          <div className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#67e8f9] to-[#9333ea] opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]" style={{ clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)' }}></div>
        </div>

        <main className="container mx-auto max-w-4xl py-12 px-4">
          <header className="text-center mb-12">
            <div className="flex justify-center items-center gap-3">
              <SparklesIcon />
              <h1 className="text-4xl sm:text-5xl font-extrabold tracking-tight bg-gradient-to-r from-purple-400 to-pink-500 text-transparent bg-clip-text">
                AI Video Idea Generator
              </h1>
            </div>
            <p className="mt-4 text-lg text-zinc-400 max-w-2xl mx-auto">
              Unlock your next viral hit. Enter a topic to brainstorm unique video ideas and craft the perfect titles.
            </p>
          </header>

          <section className="mb-12">
            <TopicInput 
              onGenerate={handleGenerateIdeas} 
              isLoading={isLoadingIdeas}
              selectedStyle={selectedStyle}
              onStyleChange={setSelectedStyle}
              isHighCreativity={isHighCreativityIdeas}
              onHighCreativityChange={setIsHighCreativityIdeas}
            />
          </section>

          {error && (
            <div className="bg-red-900/50 border border-red-700 text-red-300 px-4 py-3 rounded-lg text-center mb-8">
              <p>{error}</p>
            </div>
          )}

          <section>
            {videoIdeas.length > 0 && (
              <h2 className="text-3xl font-bold mb-8 text-center flex items-center justify-center gap-3 text-zinc-200">
                <LightbulbIcon />
                Generated Video Ideas
              </h2>
            )}
            <IdeaList
              ideas={videoIdeas}
              titleIdeas={titleIdeas}
              hooks={hooks}
              outlines={outlines}
              contentPackages={contentPackages}
              thumbnailConcepts={thumbnailConcepts}
              shortFormIdeas={shortFormIdeas}
              onGenerateTitles={handleGenerateTitles}
              onGenerateHook={handleGenerateHook}
              onGenerateOutline={handleGenerateOutline}
              onGeneratePackage={handleGeneratePackage}
              onGenerateThumbnails={handleGenerateThumbnails}
              onGenerateShorts={handleGenerateShorts}
              onRegenerateIdea={handleRegenerateIdea}
              isLoadingTitles={isLoadingTitles}
              isLoadingHook={isLoadingHook}
              isLoadingOutline={isLoadingOutline}
              isLoadingPackage={isLoadingPackage}
              isLoadingThumbnails={isLoadingThumbnails}
              isLoadingShorts={isLoadingShorts}
              isLoadingIdeas={isLoadingIdeas}
              isLoadingRegeneration={isLoadingRegeneration}
            />
          </section>
        </main>

        <div className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]" aria-hidden="true">
          <div className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-[#80ff8b] to-[#0891b2] opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]" style={{ clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)' }}></div>
        </div>
      </div>
    </div>
  );
};

export default App;
