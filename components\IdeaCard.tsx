import React, { useState } from 'react';
import { <PERSON>Idea, TitleTone, TitleLength, HookStrategy, ScriptOutline, ContentPackage, ThumbnailStyle, ShortFormIdea } from '../types';
import LoadingSpinner from './LoadingSpinner';
import TitleIcon from './icons/TitleIcon';
import ClipboardIcon from './icons/ClipboardIcon';
import CheckIcon from './icons/CheckIcon';
import MegaphoneIcon from './icons/MegaphoneIcon';
import RefreshIcon from './icons/RefreshIcon';
import ScriptIcon from './icons/ScriptIcon';
import PackageIcon from './icons/PackageIcon';
import ImageIcon from './icons/ImageIcon';
import FilmStripIcon from './icons/FilmStripIcon';

interface IdeaCardProps {
  idea: VideoIdea;
  titles: string[];
  hook?: string;
  outline?: ScriptOutline;
  contentPackage?: ContentPackage;
  thumbnailConcepts?: string[];
  shortFormIdeas?: ShortFormIdea[];
  onGenerateTitles: (tone: TitleTone, length: TitleLength, isHighCreativity: boolean, blacklistWords: string) => void;
  onGenerateHook: (strategy: HookStrategy, length: TitleLength, isHighCreativity: boolean) => void;
  onGenerateOutline: () => void;
  onGeneratePackage: () => void;
  onGenerateThumbnails: (style: ThumbnailStyle, keyElements: string) => void;
  onGenerateShorts: () => void;
  onRegenerateIdea: () => void;
  isLoadingTitles: boolean;
  isLoadingHook: boolean;
  isLoadingOutline: boolean;
  isLoadingPackage: boolean;
  isLoadingThumbnails: boolean;
  isLoadingShorts: boolean;
  isLoadingRegeneration: boolean;
}

const TitleList: React.FC<{ titles: string[], tone: TitleTone }> = ({ titles, tone }) => {
    const [copiedTitle, setCopiedTitle] = useState<string | null>(null);

    const handleCopy = (titleToCopy: string) => {
      navigator.clipboard.writeText(titleToCopy).then(() => {
        setCopiedTitle(titleToCopy);
        setTimeout(() => setCopiedTitle(null), 2000); // Reset after 2 seconds
      }).catch(err => {
        console.error('Failed to copy text: ', err);
      });
    };

    if (titles.length === 0) return null;
    return (
        <div className="mt-6 pt-6 border-t border-zinc-800">
            <h4 className="text-md font-semibold text-purple-300 mb-4 flex items-center gap-2">
                <TitleIcon />
                {tone} Title Suggestions
            </h4>
            <ul className="space-y-3 text-zinc-300">
                {titles.map((title, index) => (
                    <li key={index} className="flex justify-between items-center gap-4 group p-2 -m-2 rounded-lg transition-colors hover:bg-zinc-800/50">
                        <span className="flex-grow">{title}</span>
                        <button
                            onClick={() => handleCopy(title)}
                            aria-label={`Copy title: ${title}`}
                            className="p-2 rounded-md transition-all duration-200 ease-in-out text-zinc-400 hover:text-white hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-900 focus:ring-blue-500"
                        >
                            {copiedTitle === title ? <CheckIcon /> : <ClipboardIcon />}
                        </button>
                    </li>
                ))}
            </ul>
        </div>
    );
};

const HookDisplay: React.FC<{ hook: string, isLoading: boolean, onGenerate: () => void }> = ({ hook, isLoading, onGenerate }) => {
    const [isCopied, setIsCopied] = useState(false);

    const handleCopy = () => {
        if (!hook) return;
        navigator.clipboard.writeText(hook).then(() => {
            setIsCopied(true);
            setTimeout(() => setIsCopied(false), 2000);
        }).catch(err => {
            console.error('Failed to copy hook: ', err);
        });
    };

    if (!hook && !isLoading) {
        return null; // The controls are now outside this component
    }
    
    if (!hook && isLoading) {
         return (
            <div className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-zinc-400 bg-zinc-700/50 rounded-lg">
                <LoadingSpinner />
                Generating Hook...
            </div>
        );
    }
    
    if (!hook) return null;

    return (
        <div className="space-y-3">
            <h4 className="text-md font-semibold text-cyan-300 flex items-center gap-2">
                <MegaphoneIcon />
                Suggested Hook
            </h4>
            <div className="flex flex-col gap-3 p-3 -m-3 rounded-lg bg-zinc-800/50 border border-zinc-700/50">
                <p className="flex-grow text-zinc-300 italic">"{hook}"</p>
                 <div className="flex items-center gap-3 self-end">
                     <button
                        onClick={handleCopy}
                        aria-label="Copy hook"
                        className="p-2 rounded-md transition-all duration-200 ease-in-out text-zinc-400 hover:text-white hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-900 focus:ring-blue-500"
                    >
                        {isCopied ? <CheckIcon /> : <ClipboardIcon />}
                    </button>
                    <button
                        onClick={onGenerate}
                        disabled={isLoading}
                        className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold text-zinc-300 bg-zinc-700 rounded-md hover:bg-zinc-600 transition-colors disabled:opacity-50 disabled:cursor-wait"
                    >
                         {isLoading ? <LoadingSpinner/> : <RefreshIcon className="w-3.5 h-3.5" />}
                        Regenerate
                    </button>
                </div>
            </div>
        </div>
    );
};

const ThumbnailConceptDisplay: React.FC<{ concepts?: string[], isLoading: boolean, onGenerate: (style: ThumbnailStyle, keyElements: string) => void, ideaId: string }> = ({ concepts, isLoading, onGenerate, ideaId }) => {
    const [isCopied, setIsCopied] = useState(false);
    const [selectedStyle, setSelectedStyle] = useState<ThumbnailStyle>('General');
    const [keyElements, setKeyElements] = useState<string>('');

    const thumbnailStyles: { value: ThumbnailStyle, label: string }[] = [
        { value: 'General', label: 'General' },
        { value: 'High-Energy / Viral', label: 'High-Energy / Viral' },
        { value: 'Minimalist & Clean', label: 'Minimalist & Clean' },
        { value: 'Aesthetic & Cinematic', label: 'Aesthetic & Cinematic' },
        { value: 'Informative / Graphic', label: 'Informative / Graphic' },
    ];

    const handleGenerateClick = () => {
        onGenerate(selectedStyle, keyElements);
    };

    const formatConceptsForCopy = (conceptData: string[]): string => {
        return conceptData.map((c, i) => `Concept ${i + 1}:\n${c}`).join('\n\n');
    };

    const handleCopy = () => {
        if (!concepts || concepts.length === 0) return;
        const textToCopy = formatConceptsForCopy(concepts);
        navigator.clipboard.writeText(textToCopy).then(() => {
            setIsCopied(true);
            setTimeout(() => setIsCopied(false), 2000);
        }).catch(err => {
            console.error('Failed to copy concepts: ', err);
        });
    };

    const renderControls = () => (
        <div className="space-y-4">
            <h4 className="text-md font-semibold text-zinc-300">Thumbnail Generation</h4>
             <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Style Selector */}
                <div>
                    <label htmlFor={`thumb-style-selector-${ideaId}`} className="text-sm font-medium text-zinc-400 mb-2 block">Thumbnail Style</label>
                    <div className="relative">
                        <select
                            id={`thumb-style-selector-${ideaId}`}
                            value={selectedStyle}
                            onChange={(e) => setSelectedStyle(e.target.value as ThumbnailStyle)}
                            disabled={isLoading}
                            className="w-full appearance-none bg-zinc-800 border-2 border-zinc-700 text-zinc-300 text-sm rounded-lg py-2.5 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 focus:border-blue-500"
                            aria-label="Select thumbnail style"
                        >
                            {thumbnailStyles.map(({ value, label }) => (
                                <option key={value} value={value}>{label}</option>
                            ))}
                        </select>
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-zinc-400">
                           <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                </div>
                 {/* Key Elements Input */}
                 <div>
                    <label htmlFor={`key-elements-input-${ideaId}`} className="text-sm font-medium text-zinc-400 mb-2 block">
                        Key Elements to Include (Optional)
                    </label>
                    <input
                        id={`key-elements-input-${ideaId}`}
                        type="text"
                        value={keyElements}
                        onChange={(e) => setKeyElements(e.target.value)}
                        placeholder="e.g., my shocked face, final product"
                        className="w-full px-3 py-2 bg-zinc-800 border-2 border-zinc-700 text-zinc-300 text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 placeholder-zinc-500 focus:border-blue-500"
                        disabled={isLoading}
                        aria-label="Key elements to include in thumbnail"
                    />
                </div>
            </div>
            <button
                onClick={handleGenerateClick}
                disabled={isLoading}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-white bg-zinc-700 rounded-lg shadow-sm hover:bg-zinc-600 disabled:bg-zinc-800 disabled:text-zinc-500 disabled:cursor-wait transition-all"
            >
                <ImageIcon />
                Suggest Thumbnails
            </button>
        </div>
    );
    
    if (!concepts && !isLoading) {
        return renderControls();
    }

    if (isLoading) {
        return (
            <div className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-zinc-400 bg-zinc-700/50 rounded-lg">
                <LoadingSpinner />
                {concepts ? 'Regenerating Concepts...' : 'Generating Concepts...'}
            </div>
        );
    }
    
    if (!concepts) {
        return renderControls();
    }

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h4 className="text-md font-semibold text-indigo-300 flex items-center gap-2">
                    <ImageIcon />
                    Thumbnail Concepts
                </h4>
            </div>
            <div className="space-y-4 p-4 rounded-lg bg-zinc-800/50 border border-zinc-700/50">
                <ul className="space-y-4">
                    {concepts.map((concept, index) => (
                        <li key={index} className="flex gap-3">
                            <span className="text-indigo-300 font-bold">{index + 1}.</span>
                            <p className="text-zinc-300">{concept}</p>
                        </li>
                    ))}
                </ul>
                <div className="flex items-center gap-3 self-end border-t border-zinc-700 pt-3 mt-2 w-full justify-end">
                    <button
                        onClick={handleCopy}
                        aria-label="Copy all concepts"
                        className="p-2 rounded-md transition-all duration-200 ease-in-out text-zinc-400 hover:text-white hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-900 focus:ring-blue-500"
                    >
                        {isCopied ? <CheckIcon /> : <ClipboardIcon />}
                    </button>
                    <button
                        onClick={handleGenerateClick}
                        disabled={isLoading}
                        className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold text-zinc-300 bg-zinc-700 rounded-md hover:bg-zinc-600 transition-colors disabled:opacity-50 disabled:cursor-wait"
                    >
                        {isLoading ? <LoadingSpinner/> : <RefreshIcon className="w-3.5 h-3.5" />}
                        Regenerate
                    </button>
                </div>
            </div>
        </div>
    );
};

const OutlineDisplay: React.FC<{ outline?: ScriptOutline, isLoading: boolean, onGenerate: () => void }> = ({ outline, isLoading, onGenerate }) => {
    const [isCopied, setIsCopied] = useState(false);

    const formatOutlineForCopy = (outlineData: ScriptOutline): string => {
        const keyPoints = outlineData.keyPoints.map(p => `- ${p}`).join('\n');
        return `
HOOK
${outlineData.hook}

INTRO
${outlineData.intro}

KEY POINTS
${keyPoints}

CALL TO ACTION
${outlineData.callToAction}
        `.trim();
    };

    const handleCopy = () => {
        if (!outline) return;
        const textToCopy = formatOutlineForCopy(outline);
        navigator.clipboard.writeText(textToCopy).then(() => {
            setIsCopied(true);
            setTimeout(() => setIsCopied(false), 2000);
        }).catch(err => {
            console.error('Failed to copy outline: ', err);
        });
    };

    const renderInitialButton = () => (
        <button
            onClick={onGenerate}
            disabled={isLoading}
            className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-white bg-zinc-700 rounded-lg shadow-sm hover:bg-zinc-600 disabled:bg-zinc-800 disabled:text-zinc-500 disabled:cursor-wait transition-all"
        >
            <ScriptIcon />
            Outline Script
        </button>
    );

    if (!outline && !isLoading) {
        return renderInitialButton();
    }
    
    if (isLoading) {
        return (
            <div className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-zinc-400 bg-zinc-700/50 rounded-lg">
                <LoadingSpinner />
                {outline ? 'Regenerating Outline...' : 'Generating Outline...'}
            </div>
        );
    }
    
    if (!outline) {
        return renderInitialButton();
    }

    return (
        <div className="space-y-4">
            <h4 className="text-md font-semibold text-green-300 flex items-center gap-2">
                <ScriptIcon />
                Script Outline
            </h4>
            <div className="space-y-4 p-4 rounded-lg bg-zinc-800/50 border border-zinc-700/50">
                <div className="space-y-1">
                    <p className="font-semibold text-sm text-zinc-400">Hook</p>
                    <p className="text-zinc-300 italic">"{outline.hook}"</p>
                </div>
                <div className="space-y-1">
                    <p className="font-semibold text-sm text-zinc-400">Intro</p>
                    <p className="text-zinc-300">{outline.intro}</p>
                </div>
                <div className="space-y-2">
                    <p className="font-semibold text-sm text-zinc-400">Key Points</p>
                    <ul className="list-disc list-inside space-y-1 text-zinc-300 pl-2">
                        {outline.keyPoints.map((point, index) => <li key={index}>{point}</li>)}
                    </ul>
                </div>
                <div className="space-y-1">
                    <p className="font-semibold text-sm text-zinc-400">Call to Action</p>
                    <p className="text-zinc-300">{outline.callToAction}</p>
                </div>
                 <div className="flex items-center gap-3 self-end border-t border-zinc-700 pt-3 mt-2 w-full justify-end">
                     <button
                        onClick={handleCopy}
                        aria-label="Copy outline"
                        className="p-2 rounded-md transition-all duration-200 ease-in-out text-zinc-400 hover:text-white hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-900 focus:ring-blue-500"
                    >
                        {isCopied ? <CheckIcon /> : <ClipboardIcon />}
                    </button>
                    <button
                        onClick={onGenerate}
                        disabled={isLoading}
                        className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold text-zinc-300 bg-zinc-700 rounded-md hover:bg-zinc-600 transition-colors disabled:opacity-50 disabled:cursor-wait"
                    >
                         {isLoading ? <LoadingSpinner/> : <RefreshIcon className="w-3.5 h-3.5" />}
                        Regenerate
                    </button>
                </div>
            </div>
        </div>
    );
};

const ContentPackageDisplay: React.FC<{ pkg?: ContentPackage, isLoading: boolean, onGenerate: () => void }> = ({ pkg, isLoading, onGenerate }) => {
    const [copiedItem, setCopiedItem] = useState<'description' | 'tags' | 'tweet' | null>(null);

    const handleCopy = (item: 'description' | 'tags' | 'tweet', text: string) => {
        navigator.clipboard.writeText(text).then(() => {
            setCopiedItem(item);
            setTimeout(() => setCopiedItem(null), 2000);
        }).catch(err => {
            console.error(`Failed to copy ${item}: `, err);
        });
    };

    const renderInitialButton = () => (
        <button
            onClick={onGenerate}
            disabled={isLoading}
            className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-white bg-zinc-700 rounded-lg shadow-sm hover:bg-zinc-600 disabled:bg-zinc-800 disabled:text-zinc-500 disabled:cursor-wait transition-all"
        >
            <PackageIcon />
            Generate Content Package
        </button>
    );

    if (!pkg && !isLoading) {
        return renderInitialButton();
    }
    
    if (isLoading) {
        return (
            <div className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-zinc-400 bg-zinc-700/50 rounded-lg">
                <LoadingSpinner />
                {pkg ? 'Regenerating Package...' : 'Generating Package...'}
            </div>
        );
    }
    
    if (!pkg) {
        return renderInitialButton();
    }
    
    const tagsAsString = pkg.tags.join(', ');

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h4 className="text-md font-semibold text-orange-300 flex items-center gap-2">
                    <PackageIcon />
                    Content Package
                </h4>
                <button
                    onClick={onGenerate}
                    disabled={isLoading}
                    className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold text-zinc-300 bg-zinc-700 rounded-md hover:bg-zinc-600 transition-colors disabled:opacity-50 disabled:cursor-wait"
                >
                    {isLoading ? <LoadingSpinner/> : <RefreshIcon className="w-3.5 h-3.5" />}
                    Regenerate
                </button>
            </div>

            <div className="space-y-4 p-4 rounded-lg bg-zinc-800/50 border border-zinc-700/50">
                {/* Description */}
                <div className="space-y-2">
                    <div className="flex justify-between items-center">
                        <p className="font-semibold text-sm text-zinc-400">YouTube Description</p>
                        <button
                            onClick={() => handleCopy('description', pkg.description)}
                            aria-label="Copy description"
                            className="p-1.5 rounded-md transition-all duration-200 ease-in-out text-zinc-400 hover:text-white hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-800 focus:ring-blue-500"
                        >
                            {copiedItem === 'description' ? <CheckIcon /> : <ClipboardIcon />}
                        </button>
                    </div>
                    <pre className="text-zinc-300 text-sm whitespace-pre-wrap bg-zinc-900/50 p-3 rounded-md font-sans">{pkg.description}</pre>
                </div>

                {/* Tags */}
                <div className="space-y-2">
                     <div className="flex justify-between items-center">
                        <p className="font-semibold text-sm text-zinc-400">SEO Tags</p>
                        <button
                            onClick={() => handleCopy('tags', tagsAsString)}
                            aria-label="Copy all tags"
                            className="p-1.5 rounded-md transition-all duration-200 ease-in-out text-zinc-400 hover:text-white hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-800 focus:ring-blue-500"
                        >
                            {copiedItem === 'tags' ? <CheckIcon /> : <ClipboardIcon />}
                        </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                        {pkg.tags.map((tag, index) => (
                            <span key={index} className="bg-zinc-700 text-zinc-300 text-xs font-medium px-2.5 py-1 rounded-full">{tag}</span>
                        ))}
                    </div>
                </div>

                {/* Tweet */}
                <div className="space-y-2">
                    <div className="flex justify-between items-center">
                        <p className="font-semibold text-sm text-zinc-400">Promotional Tweet</p>
                         <button
                            onClick={() => handleCopy('tweet', pkg.tweet)}
                            aria-label="Copy tweet"
                            className="p-1.5 rounded-md transition-all duration-200 ease-in-out text-zinc-400 hover:text-white hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-800 focus:ring-blue-500"
                        >
                            {copiedItem === 'tweet' ? <CheckIcon /> : <ClipboardIcon />}
                        </button>
                    </div>
                    <blockquote className="text-zinc-300 text-sm border-l-4 border-zinc-600 pl-4 py-2 bg-zinc-900/50 rounded-r-md">
                        {pkg.tweet}
                    </blockquote>
                </div>
            </div>
        </div>
    );
};

const ShortIdeaCard: React.FC<{ idea: ShortFormIdea }> = ({ idea }) => {
    const [isCopied, setIsCopied] = useState(false);
    
    const formatForCopy = (ideaToCopy: ShortFormIdea): string => {
        const keyPoints = ideaToCopy.outline.keyPoints.map(p => `- ${p}`).join('\n');
        return `
Title: ${ideaToCopy.title}

HOOK
${ideaToCopy.outline.hook}

INTRO
${ideaToCopy.outline.intro}

KEY POINTS
${keyPoints}

CALL TO ACTION
${ideaToCopy.outline.callToAction}
        `.trim();
    };

    const handleCopy = () => {
        const textToCopy = formatForCopy(idea);
        navigator.clipboard.writeText(textToCopy).then(() => {
            setIsCopied(true);
            setTimeout(() => setIsCopied(false), 2000);
        });
    };
    
    return (
        <div className="p-4 rounded-lg bg-zinc-800/50 border border-zinc-700/50">
            <div className="flex justify-between items-start gap-2">
                <h5 className="font-bold text-zinc-200">{idea.title}</h5>
                <button
                    onClick={handleCopy}
                    aria-label="Copy script"
                    className="p-2 -m-2 rounded-md transition-all duration-200 ease-in-out text-zinc-400 hover:text-white hover:bg-zinc-700 shrink-0"
                >
                    {isCopied ? <CheckIcon /> : <ClipboardIcon />}
                </button>
            </div>
            <div className="mt-3 pt-3 border-t border-zinc-700/50 space-y-2 text-sm">
                <p><span className="font-semibold text-zinc-400">Hook:</span> <span className="text-zinc-300 italic">"{idea.outline.hook}"</span></p>
                <p><span className="font-semibold text-zinc-400">Intro:</span> <span className="text-zinc-300">"{idea.outline.intro}"</span></p>
                 <div>
                    <p className="font-semibold text-zinc-400">Key Points:</p>
                    <ul className="list-disc list-inside pl-2 space-y-1 text-zinc-300">
                        {idea.outline.keyPoints.map((point, i) => <li key={i}>{point}</li>)}
                    </ul>
                </div>
                <p><span className="font-semibold text-zinc-400">CTA:</span> <span className="text-zinc-300">{idea.outline.callToAction}</span></p>
            </div>
        </div>
    );
};

const ShortFormIdeasDisplay: React.FC<{ ideas?: ShortFormIdea[], isLoading: boolean, onGenerate: () => void }> = ({ ideas, isLoading, onGenerate }) => {
    
    const renderInitialButton = () => (
        <button
            onClick={onGenerate}
            disabled={isLoading}
            className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-white bg-zinc-700 rounded-lg shadow-sm hover:bg-zinc-600 disabled:bg-zinc-800 disabled:text-zinc-500 disabled:cursor-wait transition-all"
        >
            <FilmStripIcon />
            Create 3 Short-Form Ideas
        </button>
    );

    if (!ideas && !isLoading) {
        return renderInitialButton();
    }
    
    if (isLoading) {
        return (
            <div className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-zinc-400 bg-zinc-700/50 rounded-lg">
                <LoadingSpinner />
                {ideas ? 'Regenerating Shorts...' : 'Generating Shorts...'}
            </div>
        );
    }
    
    if (!ideas) {
        return renderInitialButton();
    }
    
    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h4 className="text-md font-semibold text-rose-300 flex items-center gap-2">
                    <FilmStripIcon />
                    Short-Form Video Ideas
                </h4>
                <button
                    onClick={onGenerate}
                    disabled={isLoading}
                    className="flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold text-zinc-300 bg-zinc-700 rounded-md hover:bg-zinc-600 transition-colors disabled:opacity-50 disabled:cursor-wait"
                >
                    {isLoading ? <LoadingSpinner/> : <RefreshIcon className="w-3.5 h-3.5" />}
                    Regenerate
                </button>
            </div>

            <div className="space-y-4 p-4 rounded-lg bg-zinc-800/50 border border-zinc-700/50">
                {ideas.map((shortIdea, index) => (
                    <ShortIdeaCard key={index} idea={shortIdea} />
                ))}
            </div>
        </div>
    );
};


const IdeaCard: React.FC<IdeaCardProps> = ({ 
    idea, 
    titles, 
    hook, 
    outline, 
    contentPackage, 
    thumbnailConcepts,
    shortFormIdeas,
    onGenerateTitles, 
    onGenerateHook, 
    onGenerateOutline, 
    onGeneratePackage, 
    onGenerateThumbnails,
    onGenerateShorts,
    onRegenerateIdea, 
    isLoadingTitles, 
    isLoadingHook, 
    isLoadingOutline, 
    isLoadingPackage, 
    isLoadingThumbnails,
    isLoadingShorts,
    isLoadingRegeneration 
}) => {
  const [selectedTone, setSelectedTone] = useState<TitleTone>('Catchy');
  const [selectedLength, setSelectedLength] = useState<TitleLength>('Standard');
  const [isHighCreativity, setIsHighCreativity] = useState<boolean>(false);
  const [blacklistWords, setBlacklistWords] = useState<string>('');

  const hookStrategies: { value: HookStrategy; label: string }[] = [
    { value: 'General', label: 'General' },
    { value: 'Problem/Solution', label: 'Problem/Solution Preview' },
    { value: 'Controversial Statement', label: 'Controversial Statement' },
    { value: 'Story Teaser', label: 'Cold Open / Story Teaser' },
    { value: 'Surprising Fact', label: 'Surprising Statistic/Fact' },
  ];
  const [selectedHookStrategy, setSelectedHookStrategy] = useState<HookStrategy>('General');
  const [selectedHookLength, setSelectedHookLength] = useState<TitleLength>('Standard');
  const [isHighCreativityHook, setIsHighCreativityHook] = useState<boolean>(false);

  const handleGenerateTitlesClick = () => {
    onGenerateTitles(selectedTone, selectedLength, isHighCreativity, blacklistWords);
  };
  
  const handleGenerateHookClick = () => {
    onGenerateHook(selectedHookStrategy, selectedHookLength, isHighCreativityHook);
  };

  return (
    <article className="bg-zinc-900 p-6 rounded-2xl border-2 border-zinc-800 shadow-lg transition-all hover:border-blue-500/50">
      <div className="flex justify-between items-start gap-4 mb-6">
        <h3 className="text-xl font-semibold text-zinc-100 flex-grow">{idea.text}</h3>
        <button
          onClick={onRegenerateIdea}
          disabled={isLoadingRegeneration}
          aria-label="Regenerate this video idea"
          className="p-2 rounded-full transition-colors text-zinc-400 hover:bg-zinc-700/50 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-zinc-900 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-wait shrink-0"
        >
          {isLoadingRegeneration ? <LoadingSpinner /> : <RefreshIcon className="h-5 w-5" />}
        </button>
      </div>
      
      {/* Tools Section */}
      <div className="border-t border-zinc-800 pt-6 flex flex-col gap-6">
        
        {/* Title Generation Controls */}
        <div className="space-y-6 bg-zinc-900/30 border border-zinc-800/50 rounded-xl p-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Length Selector */}
                <div>
                     <label htmlFor={`length-selector-${idea.id}`} className="text-sm font-medium text-zinc-400 mb-2 block">Length</label>
                     <div id={`length-selector-${idea.id}`} className="inline-flex w-full items-center bg-zinc-800 border-2 border-zinc-700 rounded-lg p-1">
                        {(['Short', 'Standard', 'Long'] as TitleLength[]).map((len) => (
                            <button
                                key={len}
                                onClick={() => setSelectedLength(len)}
                                disabled={isLoadingTitles}
                                className={`flex-1 px-3 py-1.5 text-sm rounded-md transition-colors ${
                                    selectedLength === len
                                        ? 'bg-zinc-600 text-white font-semibold shadow'
                                        : 'text-zinc-300 hover:bg-zinc-700/50'
                                }`}
                                aria-pressed={selectedLength === len}
                            >
                                {len}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Tone Selector */}
                <div>
                    <label htmlFor={`tone-selector-${idea.id}`} className="text-sm font-medium text-zinc-400 mb-2 block">Tone</label>
                    <div className="relative">
                        <select
                            id={`tone-selector-${idea.id}`}
                            value={selectedTone}
                            onChange={(e) => setSelectedTone(e.target.value as TitleTone)}
                            disabled={isLoadingTitles}
                            className="w-full appearance-none bg-zinc-800 border-2 border-zinc-700 text-zinc-300 text-sm rounded-lg py-2.5 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 focus:border-blue-500"
                            aria-label="Select title tone"
                        >
                            <option value="Catchy">Catchy</option>
                            <option value="Informative">Informative</option>
                            <option value="Question-based">Question-based</option>
                            <option value="SEO-Focused">SEO-Focused</option>
                            <option value="Humorous">Humorous</option>
                            <option value="Benefit-Driven">Benefit-Driven</option>
                            <option value="Intriguing">Intriguing</option>
                            <option value="Educational">Educational</option>
                        </select>
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-zinc-400">
                            <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                        </div>
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex items-center bg-zinc-800/70 p-3 rounded-lg border-2 border-zinc-700">
                    <label htmlFor={`high-creativity-titles-${idea.id}`} className="flex items-center gap-3 cursor-pointer">
                        <input
                            id={`high-creativity-titles-${idea.id}`}
                            type="checkbox"
                            checked={isHighCreativity}
                            onChange={(e) => setIsHighCreativity(e.target.checked)}
                            disabled={isLoadingTitles}
                            className="h-5 w-5 rounded border-zinc-600 bg-zinc-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-zinc-800"
                        />
                        <div className="flex flex-col">
                            <span className="font-medium text-zinc-200 text-sm">High Creativity</span>
                            <span className="text-xs text-zinc-400">Generates unexpected titles.</span>
                        </div>
                    </label>
                </div>
                
                <div>
                    <label htmlFor={`blacklist-input-${idea.id}`} className="text-sm font-medium text-zinc-400 mb-2 block">
                        Words to Exclude (Optional)
                    </label>
                    <input
                        id={`blacklist-input-${idea.id}`}
                        type="text"
                        value={blacklistWords}
                        onChange={(e) => setBlacklistWords(e.target.value)}
                        placeholder="e.g., boring, cheap, clickbait"
                        className="w-full px-3 py-2 bg-zinc-800 border-2 border-zinc-700 text-zinc-300 text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 placeholder-zinc-500 focus:border-blue-500"
                        disabled={isLoadingTitles}
                        aria-label="Words to exclude from titles"
                    />
                </div>
            </div>

            <div>
                <button
                    onClick={handleGenerateTitlesClick}
                    disabled={isLoadingTitles}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 text-sm font-semibold text-white bg-blue-600 rounded-lg shadow-lg hover:bg-blue-500 disabled:bg-zinc-700 disabled:text-zinc-400 disabled:cursor-wait transition-all duration-300 transform hover:scale-[1.02] disabled:scale-100"
                  >
                    {isLoadingTitles ? (
                        <>
                            <LoadingSpinner />
                            Generating Titles...
                        </>
                    ) : (
                      titles.length > 0 ? 'Regenerate Titles' : 'Get Title Ideas'
                    )}
                </button>
            </div>
        </div>

        {/* Other Content Tools */}
        <div className="pt-6 border-t border-zinc-800 space-y-6">
             <div className="space-y-4">
                <h4 className="text-md font-semibold text-zinc-300">Hook Generation</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {/* Hook Length Selector */}
                    <div>
                        <label htmlFor={`hook-length-selector-${idea.id}`} className="text-sm font-medium text-zinc-400 mb-2 block">Hook Length</label>
                        <div id={`hook-length-selector-${idea.id}`} className="inline-flex w-full items-center bg-zinc-800 border-2 border-zinc-700 rounded-lg p-1">
                            {(['Short', 'Standard', 'Long'] as TitleLength[]).map((len) => (
                                <button
                                    key={len}
                                    onClick={() => setSelectedHookLength(len)}
                                    disabled={isLoadingHook}
                                    className={`flex-1 px-3 py-1.5 text-sm rounded-md transition-colors ${
                                        selectedHookLength === len
                                            ? 'bg-zinc-600 text-white font-semibold shadow'
                                            : 'text-zinc-300 hover:bg-zinc-700/50'
                                    }`}
                                    aria-pressed={selectedHookLength === len}
                                >
                                    {len}
                                </button>
                            ))}
                        </div>
                    </div>

                    {/* Hook Strategy Selector */}
                    <div>
                        <label htmlFor={`hook-strategy-selector-${idea.id}`} className="text-sm font-medium text-zinc-400 mb-2 block">Hook Strategy</label>
                        <div className="relative">
                            <select
                                id={`hook-strategy-selector-${idea.id}`}
                                value={selectedHookStrategy}
                                onChange={(e) => setSelectedHookStrategy(e.target.value as HookStrategy)}
                                disabled={isLoadingHook}
                                className="w-full appearance-none bg-zinc-800 border-2 border-zinc-700 text-zinc-300 text-sm rounded-lg py-2.5 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 focus:border-blue-500"
                                aria-label="Select hook strategy"
                            >
                                {hookStrategies.map(({ value, label }) => (
                                    <option key={value} value={value}>{label}</option>
                                ))}
                            </select>
                            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-zinc-400">
                                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/></svg>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div className="flex items-center bg-zinc-800/70 p-3 rounded-lg border-2 border-zinc-700">
                    <label htmlFor={`high-creativity-hook-${idea.id}`} className="flex items-center gap-3 cursor-pointer w-full">
                        <input
                            id={`high-creativity-hook-${idea.id}`}
                            type="checkbox"
                            checked={isHighCreativityHook}
                            onChange={(e) => setIsHighCreativityHook(e.target.checked)}
                            disabled={isLoadingHook}
                            className="h-5 w-5 rounded border-zinc-600 bg-zinc-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-zinc-800"
                        />
                        <div className="flex flex-col">
                            <span className="font-medium text-zinc-200 text-sm">High Creativity</span>
                            <span className="text-xs text-zinc-400">Generates unexpected hooks.</span>
                        </div>
                    </label>
                </div>
                
                {!hook && (
                    <button
                        onClick={handleGenerateHookClick}
                        disabled={isLoadingHook}
                        className="w-full flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-white bg-zinc-700 rounded-lg shadow-sm hover:bg-zinc-600 disabled:bg-zinc-800 disabled:text-zinc-500 disabled:cursor-wait transition-all"
                    >
                        {isLoadingHook ? (
                            <>
                                <LoadingSpinner />
                                Generating Hook...
                            </>
                        ) : (
                            <>
                                <MegaphoneIcon />
                                Generate Hook
                            </>
                        )}
                    </button>
                )}
                
                <HookDisplay hook={hook || ''} isLoading={isLoadingHook} onGenerate={handleGenerateHookClick} />
            </div>

            <ThumbnailConceptDisplay 
                concepts={thumbnailConcepts} 
                isLoading={isLoadingThumbnails} 
                onGenerate={onGenerateThumbnails} 
                ideaId={idea.id}
            />
            <ShortFormIdeasDisplay
              ideas={shortFormIdeas}
              isLoading={isLoadingShorts}
              onGenerate={onGenerateShorts}
            />
            <OutlineDisplay outline={outline} isLoading={isLoadingOutline} onGenerate={onGenerateOutline} />
            <ContentPackageDisplay pkg={contentPackage} isLoading={isLoadingPackage} onGenerate={onGeneratePackage} />
        </div>

      </div>

      <TitleList titles={titles} tone={selectedTone} />
    </article>
  );
};

export default IdeaCard;