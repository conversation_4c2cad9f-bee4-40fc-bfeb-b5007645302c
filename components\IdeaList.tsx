import React from 'react';
import { VideoIdea, TitleIdeas, TitleTone, TitleLength, HookStrategy, ScriptOutline, ContentPackage, ThumbnailStyle, ShortFormIdea } from '../types';
import IdeaCard from './IdeaCard';

interface IdeaListProps {
  ideas: VideoIdea[];
  titleIdeas: TitleIdeas;
  hooks: Record<string, string>;
  outlines: Record<string, ScriptOutline>;
  contentPackages: Record<string, ContentPackage>;
  thumbnailConcepts: Record<string, string[]>;
  shortFormIdeas: Record<string, ShortFormIdea[]>;
  onGenerateTitles: (idea: VideoIdea, tone: TitleTone, length: TitleLength, isHighCreativity: boolean, blacklistWords: string) => void;
  onGenerateHook: (idea: VideoIdea, strategy: HookStrategy, length: TitleLength, isHighCreativity: boolean) => void;
  onGenerateOutline: (idea: VideoIdea) => void;
  onGeneratePackage: (idea: VideoIdea) => void;
  onGenerateThumbnails: (idea: VideoIdea, style: ThumbnailStyle, keyElements: string) => void;
  onGenerateShorts: (idea: VideoIdea) => void;
  onRegenerateIdea: (ideaId: string) => void;
  isLoadingIdeas: boolean;
  isLoadingTitles: Record<string, boolean>;
  isLoadingHook: Record<string, boolean>;
  isLoadingOutline: Record<string, boolean>;
  isLoadingPackage: Record<string, boolean>;
  isLoadingThumbnails: Record<string, boolean>;
  isLoadingShorts: Record<string, boolean>;
  isLoadingRegeneration: Record<string, boolean>;
}

const IdeaCardSkeleton: React.FC = () => (
    <div className="bg-zinc-900 p-6 rounded-2xl border-2 border-zinc-800 animate-pulse">
        <div className="h-6 bg-zinc-800 rounded w-3/4 mb-6"></div>
        <div className="h-px bg-zinc-800 my-6"></div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
                <div className="h-4 bg-zinc-800 rounded w-1/4"></div>
                <div className="h-10 bg-zinc-800 rounded-lg w-full"></div>
            </div>
            <div className="space-y-2">
                <div className="h-4 bg-zinc-800 rounded w-1/4"></div>
                <div className="h-10 bg-zinc-800 rounded-lg w-full"></div>
            </div>
        </div>
        <div className="h-12 bg-zinc-800 rounded-lg w-full mt-6"></div>
    </div>
)

const IdeaList: React.FC<IdeaListProps> = ({ 
    ideas, 
    titleIdeas, 
    hooks, 
    outlines, 
    contentPackages, 
    thumbnailConcepts,
    shortFormIdeas,
    onGenerateTitles, 
    onGenerateHook, 
    onGenerateOutline, 
    onGeneratePackage, 
    onGenerateThumbnails,
    onGenerateShorts,
    onRegenerateIdea, 
    isLoadingIdeas, 
    isLoadingTitles, 
    isLoadingHook, 
    isLoadingOutline, 
    isLoadingPackage, 
    isLoadingThumbnails,
    isLoadingShorts,
    isLoadingRegeneration 
}) => {
  if (isLoadingIdeas) {
    return (
        <div className="space-y-6">
            {[...Array(5)].map((_, i) => <IdeaCardSkeleton key={i} />)}
        </div>
    );
  }

  if (!ideas.length) {
    return null;
  }

  return (
    <div className="space-y-6">
      {ideas.map(idea => (
        <IdeaCard
          key={idea.id}
          idea={idea}
          titles={titleIdeas[idea.id] || []}
          hook={hooks[idea.id]}
          outline={outlines[idea.id]}
          contentPackage={contentPackages[idea.id]}
          thumbnailConcepts={thumbnailConcepts[idea.id]}
          shortFormIdeas={shortFormIdeas[idea.id]}
          onGenerateTitles={(tone, length, isHighCreativity, blacklistWords) => onGenerateTitles(idea, tone, length, isHighCreativity, blacklistWords)}
          onGenerateHook={(strategy, length, isHighCreativity) => onGenerateHook(idea, strategy, length, isHighCreativity)}
          onGenerateOutline={() => onGenerateOutline(idea)}
          onGeneratePackage={() => onGeneratePackage(idea)}
          onGenerateThumbnails={(style, keyElements) => onGenerateThumbnails(idea, style, keyElements)}
          onGenerateShorts={() => onGenerateShorts(idea)}
          onRegenerateIdea={() => onRegenerateIdea(idea.id)}
          isLoadingTitles={isLoadingTitles[idea.id] || false}
          isLoadingHook={isLoadingHook[idea.id] || false}
          isLoadingOutline={isLoadingOutline[idea.id] || false}
          isLoadingPackage={isLoadingPackage[idea.id] || false}
          isLoadingThumbnails={isLoadingThumbnails[idea.id] || false}
          isLoadingShorts={isLoadingShorts[idea.id] || false}
          isLoadingRegeneration={isLoadingRegeneration[idea.id] || false}
        />
      ))}
    </div>
  );
};

export default IdeaList;