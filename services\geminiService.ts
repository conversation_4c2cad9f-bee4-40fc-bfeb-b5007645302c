import { GoogleGenAI, Type } from "@google/genai";
import { <PERSON><PERSON>one, TitleLength, VideoStyle, HookStrategy, ScriptOutline, ContentPackage, ThumbnailStyle, ShortFormIdea } from "../types";

if (!process.env.API_KEY) {
    throw new Error("API_KEY environment variable not set");
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });
const model = "gemini-2.5-flash";

const parseJsonResponse = <T,>(text: string, key?: string): T => {
    try {
        const parsed = JSON.parse(text);
        return key ? parsed[key] : parsed;
    } catch (e) {
        console.error("Failed to parse JSON response:", text);
        throw new Error("AI returned an invalid response format.");
    }
};

export const generateVideoIdeas = async (topic: string, style: VideoStyle, isHighCreativity: boolean): Promise<string[]> => {
    const styleInstruction = style === 'General'
        ? 'The ideas should cover a diverse range of styles and formats.'
        : `Each idea should be in a '${style}' format.`;
    
    const creativityInstruction = isHighCreativity
        ? 'Prioritize extremely creative, unconventional, and surprising ideas that break the mold. Think completely outside the box.'
        : 'Provide a mix of practical and creative ideas.';

    const response = await ai.models.generateContent({
        model,
        contents: `You are a creative strategist for YouTube content. Based on the topic '${topic}', generate a JSON object with a key "ideas" containing an array of 5 unique and compelling video ideas. ${styleInstruction} ${creativityInstruction} Each idea should be a concise string.`,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.OBJECT,
                properties: {
                    ideas: {
                        type: Type.ARRAY,
                        items: {
                            type: Type.STRING,
                            description: "A single video idea."
                        }
                    }
                },
                required: ["ideas"],
            },
        },
    });
    
    return parseJsonResponse<string[]>(response.text, 'ideas');
};

export const generateTitleIdeas = async (videoIdea: string, tone: TitleTone, length: TitleLength, isHighCreativity: boolean, blacklistWords: string, topic?: string): Promise<string[]> => {
    let lengthInstruction = '';
    switch (length) {
        case 'Short':
            lengthInstruction = 'The titles must be very short and punchy, ideally under 40 characters.';
            break;
        case 'Long':
            lengthInstruction = 'The titles should be longer and more descriptive, potentially over 70 characters, providing more context to the viewer.';
            break;
        case 'Standard':
        default:
            // No specific length instruction for standard, maintains current behavior.
            break;
    }

    const creativityInstruction = isHighCreativity
        ? 'The titles must be exceptionally creative, using clever wordplay, unexpected angles, and a highly original voice to maximize intrigue and stand out.'
        : 'The titles should be effective and engaging, following best practices for the chosen tone.';

    const blacklistInstruction = blacklistWords.trim()
        ? `Crucially, the titles MUST NOT contain any of the following case-insensitive words: ${blacklistWords}.`
        : '';

    let prompt = `You are an expert in writing viral YouTube titles. For the video idea '${videoIdea}', generate a JSON object with a key "titles" containing an array of 10 high-click-through-rate (CTR) titles. The titles must have a '${tone}' tone. ${lengthInstruction} ${creativityInstruction} ${blacklistInstruction} Each title should be a concise string.`;

    if (topic && topic.trim()) {
        prompt += ` Pay special attention to including relevant SEO keywords related to the main topic: '${topic}'. Make the titles highly discoverable for people searching for content about '${topic}'.`;
    }
    
    const response = await ai.models.generateContent({
        model,
        contents: prompt,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.OBJECT,
                properties: {
                    titles: {
                        type: Type.ARRAY,
                        items: {
                            type: Type.STRING,
                            description: "A single catchy title."
                        }
                    }
                },
                required: ["titles"],
            },
        },
    });
    
    return parseJsonResponse<string[]>(response.text, 'titles');
};

export const generateVideoHook = async (videoIdea: string, strategy: HookStrategy, length: TitleLength, isHighCreativity: boolean, topic?: string): Promise<string> => {
    let strategyInstruction = '';
    switch (strategy) {
        case 'Problem/Solution':
            strategyInstruction = 'The hook must state a common problem or pain point and tease the solution that the video provides.';
            break;
        case 'Controversial Statement':
            strategyInstruction = 'The hook must be a bold, debatable, or controversial statement designed to provoke curiosity and make the viewer question their assumptions.';
            break;
        case 'Story Teaser':
            strategyInstruction = 'The hook must drop the viewer right into the middle of a story or an exciting moment, teasing what happened (a "cold open").';
            break;
        case 'Surprising Fact':
            strategyInstruction = 'The hook must start with a shocking, little-known, or surprising statistic or fact that is directly relevant to the video idea.';
            break;
        case 'General':
        default:
            strategyInstruction = 'The hook should be a compelling, general-purpose opener designed to grab attention.';
            break;
    }

    let lengthInstruction = '';
    switch (length) {
        case 'Short':
            lengthInstruction = 'The hook must be very short and punchy, ideally one sentence under 10 words.';
            break;
        case 'Long':
            lengthInstruction = 'The hook can be a bit longer, up to 2-3 sentences, building more anticipation.';
            break;
        case 'Standard':
        default:
            lengthInstruction = 'The hook should be a standard length, around 1-2 sentences.';
            break;
    }

    const creativityInstruction = isHighCreativity
        ? 'The hook must be exceptionally creative and unconventional, designed to immediately surprise the viewer.'
        : 'The hook should be effective and follow best practices for grabbing attention.';

    let prompt = `You are an expert YouTube scriptwriter. For the video idea '${videoIdea}', generate a JSON object with a key "hook" containing a single, compelling hook. A hook is the first 5-10 seconds of a video, designed to grab the viewer's attention immediately and make them want to watch more. ${strategyInstruction} ${lengthInstruction} ${creativityInstruction}`;

    if (topic && topic.trim()) {
        prompt += ` The hook should be relevant to the main topic: '${topic}'.`;
    }

    const response = await ai.models.generateContent({
        model,
        contents: prompt,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.OBJECT,
                properties: {
                    hook: {
                        type: Type.STRING,
                        description: "A single compelling video hook."
                    }
                },
                required: ["hook"],
            },
        },
    });

    return parseJsonResponse<string>(response.text, 'hook');
};

export const generateScriptOutline = async (videoIdea: string, topic?: string): Promise<ScriptOutline> => {
    let prompt = `You are an expert YouTube scriptwriter and content strategist. For the video idea '${videoIdea}', generate a structured script outline. The outline must be a JSON object with the keys "hook", "intro", "keyPoints", and "callToAction".
- "hook": A compelling opening sentence to grab attention.
- "intro": A short introduction (1-2 sentences) explaining what the video is about and what the viewer will learn.
- "keyPoints": An array of 3-5 main talking points or sections that will be covered in the video. Each point must be a concise string.
- "callToAction": A concluding sentence that encourages viewers to like, subscribe, or comment.`;

    if (topic && topic.trim()) {
        prompt += ` The outline should be optimized for the main topic: '${topic}'.`;
    }

    const response = await ai.models.generateContent({
        model,
        contents: prompt,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.OBJECT,
                properties: {
                    hook: { type: Type.STRING },
                    intro: { type: Type.STRING },
                    keyPoints: {
                        type: Type.ARRAY,
                        items: { type: Type.STRING }
                    },
                    callToAction: { type: Type.STRING }
                },
                required: ["hook", "intro", "keyPoints", "callToAction"],
            },
        },
    });
    
    return parseJsonResponse<ScriptOutline>(response.text);
};

export const generateContentPackage = async (videoIdea: string, topic?: string): Promise<ContentPackage> => {
    let prompt = `You are an expert YouTube content strategist and social media manager. For the video idea '${videoIdea}', generate a complete content package. The package must be a JSON object with the keys "description", "tags", and "tweet".
- "description": An SEO-optimized YouTube description. It must be engaging, include relevant keywords, and use placeholders like [INSERT SOCIAL MEDIA LINK] and [INSERT RELATED VIDEO LINK]. Structure it with a clear summary, and suggest where timestamps could go.
- "tags": An array of 10-15 highly relevant SEO tags (keywords) for YouTube.
- "tweet": A short, catchy promotional tweet (under 280 characters) with relevant hashtags to share the video on X/Twitter.`;

    if (topic && topic.trim()) {
        prompt += ` The entire package should be optimized for the main topic: '${topic}'.`;
    }

    const response = await ai.models.generateContent({
        model,
        contents: prompt,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.OBJECT,
                properties: {
                    description: { type: Type.STRING },
                    tags: {
                        type: Type.ARRAY,
                        items: { type: Type.STRING }
                    },
                    tweet: { type: Type.STRING }
                },
                required: ["description", "tags", "tweet"],
            },
        },
    });
    
    return parseJsonResponse<ContentPackage>(response.text);
};

export const generateThumbnailConcepts = async (videoIdea: string, topic: string | undefined, style: ThumbnailStyle, keyElements: string): Promise<string[]> => {
    let styleInstruction = '';
    switch (style) {
        case 'High-Energy / Viral':
            styleInstruction = 'The concepts must be high-energy and designed to go viral, suggesting bright colors, expressive faces, bold text, and clear visual indicators like arrows or circles.';
            break;
        case 'Minimalist & Clean':
            styleInstruction = 'The concepts must be minimalist and clean, suggesting a lot of negative space, simple typography, and a strong focus on a single key object or subject.';
            break;
        case 'Aesthetic & Cinematic':
            styleInstruction = 'The concepts must be aesthetic and cinematic, focusing on beautiful imagery, high-quality photography, and elegant fonts. Text should be used sparingly.';
            break;
        case 'Informative / Graphic':
            styleInstruction = 'The concepts must be informative, suggesting the use of graphics, charts, "before & after" comparisons, or other visual data to convey information clearly.';
            break;
        case 'General':
        default:
             // No specific style instruction for general.
            break;
    }

    const keyElementsInstruction = keyElements.trim()
        ? `Crucially, the concepts MUST incorporate the following key elements: ${keyElements}.`
        : '';
    
    let prompt = `You are a viral marketing expert specializing in YouTube thumbnails. For the video idea '${videoIdea}', generate a JSON object with a key "concepts" containing an array of 3 distinct, high-click-through-rate (CTR) thumbnail concepts. Each concept must be a concise, vivid text description of a potential thumbnail image, focusing on visual elements, text overlays, and emotional impact. ${styleInstruction} ${keyElementsInstruction}`;

    if (topic && topic.trim()) {
        prompt += ` The concepts should be highly relevant to the main topic: '${topic}'.`;
    }

    const response = await ai.models.generateContent({
        model,
        contents: prompt,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.OBJECT,
                properties: {
                    concepts: {
                        type: Type.ARRAY,
                        items: {
                            type: Type.STRING,
                            description: "A single thumbnail concept description."
                        }
                    }
                },
                required: ["concepts"],
            },
        },
    });
    
    return parseJsonResponse<string[]>(response.text, 'concepts');
};

export const regenerateVideoIdea = async (
    topic: string, 
    style: VideoStyle, 
    isHighCreativity: boolean, 
    existingIdeas: string[]
): Promise<string> => {
    const styleInstruction = style === 'General'
        ? 'The idea can be in any style or format.'
        : `The idea must be in a '${style}' format.`;
    
    const creativityInstruction = isHighCreativity
        ? 'The idea must be extremely creative, unconventional, and surprising.'
        : 'The idea should be a practical and creative concept.';
        
    const existingIdeasInstruction = `It must be a new idea, completely different from the following existing ideas: ${existingIdeas.map(i => `"${i}"`).join(', ')}.`;

    const response = await ai.models.generateContent({
        model,
        contents: `You are a creative strategist for YouTube content. Based on the topic '${topic}', generate a JSON object with a key "idea" containing a single, unique, and compelling video idea. ${styleInstruction} ${creativityInstruction} ${existingIdeasInstruction}`,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.OBJECT,
                properties: {
                    idea: {
                        type: Type.STRING,
                        description: "A single, new video idea."
                    }
                },
                required: ["idea"],
            },
        },
    });
    
    return parseJsonResponse<string>(response.text, 'idea');
};

export const generateShortFormIdeas = async (videoIdea: string, topic?: string): Promise<ShortFormIdea[]> => {
    let prompt = `You are a creative strategist specializing in short-form vertical video content (like YouTube Shorts, TikTok, Reels). Based on the long-form video idea '${videoIdea}', generate a JSON object with a key "shortIdeas" containing an array of 3 distinct short-form video concepts that could be created from it.

For each short-form idea, provide a catchy "title" and a concise "outline". The outline must be a JSON object with the keys "hook", "intro", "keyPoints", and "callToAction".
- "title": A short, viral-style title for the short-form video.
- "outline":
    - "hook": A powerful, attention-grabbing opening sentence (3-5 seconds).
    - "intro": A very brief sentence setting the context.
    - "keyPoints": An array of 1-3 quick, punchy talking points or visual sequences.
    - "callToAction": A simple call to action, like "Follow for more!" or pointing to the full video.`;

    if (topic && topic.trim()) {
        prompt += ` The short-form ideas should be relevant to the main topic: '${topic}'.`;
    }

    const response = await ai.models.generateContent({
        model,
        contents: prompt,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.OBJECT,
                properties: {
                    shortIdeas: {
                        type: Type.ARRAY,
                        items: {
                            type: Type.OBJECT,
                            properties: {
                                title: { type: Type.STRING },
                                outline: {
                                    type: Type.OBJECT,
                                    properties: {
                                        hook: { type: Type.STRING },
                                        intro: { type: Type.STRING },
                                        keyPoints: {
                                            type: Type.ARRAY,
                                            items: { type: Type.STRING }
                                        },
                                        callToAction: { type: Type.STRING }
                                    },
                                    required: ["hook", "intro", "keyPoints", "callToAction"]
                                }
                            },
                            required: ["title", "outline"]
                        }
                    }
                },
                required: ["shortIdeas"]
            },
        },
    });
    
    return parseJsonResponse<ShortFormIdea[]>(response.text, 'shortIdeas');
};